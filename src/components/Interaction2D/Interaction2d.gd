extends Area2D
class_name Interaction2d

# Signals
signal interaction_added(interaction: Interaction2d)
signal interaction_lost(interaction: Interaction2d)

# Private variables
var _interactions: Array[Interaction2d] = []

func _ready() -> void:
	area_entered.connect(_on_area_entered)
	area_exited.connect(_on_area_exited)

func interactions() -> Array[Interaction2d]:
	return _interactions.duplicate()

func enter_interaction(with: Interaction2d) -> void:
	_interactions.append(with)
	interaction_added.emit(with)

func leave_interaction(with: Interaction2d) -> void:
	# Remove this from the other interaction's list
	var i = with._interactions.find(self)
	if i >= 0 and i < with._interactions.size():
		with._interactions.remove_at(i)
		with.interaction_lost.emit(self)
	
	# Remove the other interaction from this list
	i = _interactions.find(with)
	if i >= 0 and i < _interactions.size():
		_interactions.remove_at(i)
		interaction_lost.emit(with)

func poked(from: Interaction2d) -> void:
	if not monitoring:
		return
	
	if not _interactions.has(from) and get_overlapping_areas().has(from):
		enter_interaction(from)
		from.enter_interaction(self)

func monitor(on: bool = true) -> void:
	if on:
		monitoring = true
		for area in get_overlapping_areas():
			if area is Interaction2d and not _interactions.has(area):
				_on_area_entered(area)
	else:
		monitoring = false
		var interactions_to_remove = _interactions.duplicate()
		for interaction in interactions_to_remove:
			leave_interaction(interaction)

func _on_area_entered(area: Area2D) -> void:
	if area.has_method("poked"):
		area.poked(self)

func _on_area_exited(area: Area2D) -> void:
	if area.has_method("leave_interaction"):
		area.leave_interaction(self)
