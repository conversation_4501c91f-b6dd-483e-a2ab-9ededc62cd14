extends Label

@export var start_lap_interaction: Interaction2d
@export var racer: Racecar

var lap_count: int = 0
var lap_time: float = 0.0
var fastest_lap_time: float = INF

var previous_interactions: Array

func _process(delta: float) -> void:
	if start_lap_interaction.interactions().size() > 0 and previous_interactions.size() == 0:
		if lap_count >= 1:
			fastest_lap_time = min(fastest_lap_time, lap_time)
		lap_count += 1
		lap_time = 0.0
	
	if lap_count > 1:
		text = "Speed: %.2f\nLap: %d\nTime: %.2f\nFastest Lap: %.2f" % [
			racer.velocity.length(),
			lap_count,
			lap_time,
			fastest_lap_time
		]
	elif lap_count == 1:
		text = "Speed: %.2f\nLap: %d\nTime: %.2f\n" % [
			racer.velocity.length(),
			lap_count,
			lap_time
		]
	
	lap_time += delta
	previous_interactions = start_lap_interaction.interactions()
